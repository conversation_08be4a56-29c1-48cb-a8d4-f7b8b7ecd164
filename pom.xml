<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.desaysv.device</groupId>
    <artifactId>ad4704</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <lombok.version>1.18.22</lombok.version>
    </properties>

    <dependencies>
        <!-- 添加Automation.BDaq依赖 -->
        <dependency>
            <groupId>com.automation</groupId>
            <artifactId>bdaq</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/Automation.BDaq.jar</systemPath>
        </dependency>
        <!-- 修改jtransforms依赖 -->
        <!-- https://mvnrepository.com/artifact/com.github.wendykierp/JTransforms -->
        <dependency>
            <groupId>com.github.wendykierp</groupId>
            <artifactId>JTransforms</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>

        <!-- SLF4J API + Logback实现 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.7</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.3.0</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>1.3.0</version>
        </dependency>


    </dependencies>

    <build>
        <resources>
            <!-- 包含标准的src/main/resources目录 -->
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <!-- 包含lib目录中的dll文件到jar包的/lib路径下 -->
            <resource>
                <directory>lib</directory>
                <targetPath>lib</targetPath>
                <includes>
                    <include>*.dll</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.4.2</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>com.desaysv.Usb4704Recorder</mainClass>
                        </manifest>
                    </archive>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>