@echo off
echo 开始编译和打包...

REM 检查Maven是否可用
where mvn >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误：Maven未找到，请确保Maven已安装并添加到PATH中
    pause
    exit /b 1
)

echo 清理项目...
call mvn clean

echo 编译项目...
call mvn compile
if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 打包项目...
call mvn package
if %ERRORLEVEL% NEQ 0 (
    echo 打包失败！
    pause
    exit /b 1
)

echo 构建完成！
echo 生成的jar文件位于 target/ 目录中
pause
