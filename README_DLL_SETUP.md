# USB4704 DLL 依赖配置说明

## 问题描述
当运行USB4704Recorder时，可能会遇到以下错误：
```
java.lang.UnsatisfiedLinkError: no biodaq in java.library.path
```

这是因为`Automation.BDaq.dll`依赖于`biodaq.dll`库，但系统找不到该依赖库。

## 解决方案

### 方案1：安装研华DAQ驱动（推荐）
1. 从研华官网下载并安装完整的DAQ驱动程序
2. 安装后，biodaq.dll会自动添加到系统PATH中
3. 重启应用程序即可正常使用

### 方案2：手动配置biodaq.dll
如果无法安装完整驱动，可以手动配置：

1. **获取biodaq.dll文件**
   - 从已安装研华驱动的机器上复制
   - 或从研华驱动安装包中提取

2. **放置biodaq.dll到以下位置之一：**
   - 与jar包相同的目录
   - 系统PATH目录（如 `C:\Windows\System32`）
   - 项目的lib目录（开发环境）

3. **验证配置**
   ```bash
   # 检查biodaq.dll是否在PATH中
   where biodaq.dll
   ```

### 方案3：设置java.library.path
启动应用时添加参数：
```bash
java -Djava.library.path="C:\path\to\dll\directory" -jar your-app.jar
```

## 项目配置说明

### 开发环境
- DLL文件放在 `lib/` 目录下
- 代码会优先从外部lib目录加载

### 生产环境（jar包）
- DLL文件会被打包到jar包的 `/lib/` 路径下
- 运行时自动提取到临时目录并加载
- 需要确保biodaq.dll在系统PATH中可用

## 代码改进说明

新的DLL加载机制包含以下特性：

1. **智能加载策略**
   - 优先从外部lib目录加载（开发环境）
   - 自动从jar包提取到临时目录（生产环境）

2. **依赖处理**
   - 自动尝试加载biodaq依赖库
   - 提供详细的错误信息和解决建议

3. **路径管理**
   - 动态添加临时目录到java.library.path
   - 自动清理临时文件

4. **错误处理**
   - 详细的错误日志
   - 明确的解决方案提示

## 常见问题

### Q: 为什么需要biodaq.dll？
A: Automation.BDaq.dll是研华提供的Java接口库，它内部调用了底层的biodaq.dll来实现硬件通信。

### Q: 可以将biodaq.dll也打包到jar中吗？
A: 可以，但需要修改代码来同时提取和加载biodaq.dll。建议使用系统安装的方式更稳定。

### Q: 如何确认biodaq.dll版本兼容性？
A: 确保biodaq.dll与Automation.BDaq.dll来自同一个驱动版本，避免版本不匹配问题。

## 测试验证

运行应用后，查看日志输出：
- 成功：`DLL加载成功: xxx`
- 失败：会显示详细的错误信息和解决建议

如果仍有问题，请检查：
1. 研华驱动是否正确安装
2. biodaq.dll是否在系统PATH中
3. DLL文件是否损坏或版本不匹配
