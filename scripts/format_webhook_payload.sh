#!/bin/bash

# 读取GitLab传递的环境变量
EVENT_NAME=$CI_JOB_NAME
PROJECT_NAME=$CI_PROJECT_NAME
COMMIT_SHA=$CI_COMMIT_SHA
COMMIT_MESSAGE=$CI_COMMIT_MESSAGE
COMMIT_AUTHOR=$CI_COMMIT_AUTHOR

# 初始化变量
COMMIT_STATS_LINE=""
ADDITIONAL_INFO_FOR_MSG=""

# 检查是否为合并提交 (通过查找第二个父提交)
# PARENT_2_SHA 将会是第二个父提交的SHA，如果存在的话
PARENT_2_SHA=$(git rev-parse --quiet --verify ${CI_COMMIT_SHA}^2 2>/dev/null)

if [ -n "$PARENT_2_SHA" ]; then
  # 是合并提交
  PARENT_1_SHA=$(git rev-parse ${CI_COMMIT_SHA}^1) # 第一个父提交，合并的目标分支的先前状态

  # --- 新逻辑：只统计 $COMMIT_AUTHOR 的贡献 ---
  TOTAL_LINES_ADDED_BY_AUTHOR=0
  TOTAL_LINES_DELETED_BY_AUTHOR=0
  declare -A FILES_CHANGED_MAP_BY_AUTHOR # 使用关联数组统计唯一文件

  # 提取作者名字部分（去掉邮箱）用于更好的匹配
  AUTHOR_NAME=$(echo "$COMMIT_AUTHOR" | sed 's/<.*>//' | sed 's/[[:space:]]*$//')
  
  # 获取合并基点
  MERGE_BASE=$(git merge-base $PARENT_1_SHA $PARENT_2_SHA 2>/dev/null)
  
  # 获取 $COMMIT_AUTHOR 在本次合并中引入的非合并提交的行数变更
  # 修复：搜索更全面的范围，确保包含所有相关提交
  STAT_DATA_LINES=""
  
  # 方法1：获取该作者在整个合并过程中的所有非合并提交
  # 使用 git log 的 --first-parent 和 --ancestry-path 选项来获取更准确的结果
  if [ -n "$MERGE_BASE" ]; then
    # 获取该作者在两个分支上的所有提交
    ALL_COMMITS_BY_AUTHOR=$(git log --author="$COMMIT_AUTHOR" --no-merges --pretty="format:%H" $MERGE_BASE..$PARENT_1_SHA $MERGE_BASE..$PARENT_2_SHA 2>/dev/null | sort -u || true)
    
    # 如果完整作者匹配没有结果，尝试只用名字匹配
    if [ -z "$ALL_COMMITS_BY_AUTHOR" ]; then
      ALL_COMMITS_BY_AUTHOR=$(git log --author="$AUTHOR_NAME" --no-merges --pretty="format:%H" $MERGE_BASE..$PARENT_1_SHA $MERGE_BASE..$PARENT_2_SHA 2>/dev/null | sort -u || true)
    fi
    
    # 对每个找到的提交，获取其统计信息
    if [ -n "$ALL_COMMITS_BY_AUTHOR" ]; then
      while IFS= read -r commit_hash; do
        if [ -n "$commit_hash" ]; then
          commit_stats=$(git show --numstat --pretty="format:" $commit_hash | grep -v '^$' || true)
          if [ -n "$commit_stats" ]; then
            if [ -n "$STAT_DATA_LINES" ]; then
              STAT_DATA_LINES="$STAT_DATA_LINES"$'\n'"$commit_stats"
            else
              STAT_DATA_LINES="$commit_stats"
            fi
          fi
        fi
      done <<< "$ALL_COMMITS_BY_AUTHOR"
    fi
  fi
  
  # 方法2：如果上面的方法还是没有结果，尝试更直接的方法
  if [ -z "$STAT_DATA_LINES" ]; then
    # 直接使用git log获取该作者的所有相关提交统计
    STAT_DATA_LINES=$(git log --author="$COMMIT_AUTHOR" --no-merges --numstat --pretty="format:" $PARENT_1_SHA..$CI_COMMIT_SHA | grep -v '^-' | grep -v '^$' || true)
    
    # 如果没有结果，尝试只用作者名字匹配
    if [ -z "$STAT_DATA_LINES" ]; then
      STAT_DATA_LINES=$(git log --author="$AUTHOR_NAME" --no-merges --numstat --pretty="format:" $PARENT_1_SHA..$CI_COMMIT_SHA | grep -v '^-' | grep -v '^$' || true)
    fi
  fi
  
  # 方法3：最后的尝试 - 直接从整个合并中查找
  if [ -z "$STAT_DATA_LINES" ] && [ -n "$MERGE_BASE" ]; then
    # 获取从合并基点到两个父提交之间该作者的所有提交
    STAT_DATA_LINES_FULL=$(git log --author="$COMMIT_AUTHOR" --no-merges --numstat --pretty="format:" ${MERGE_BASE}..${PARENT_1_SHA} ${MERGE_BASE}..${PARENT_2_SHA} | grep -v '^-' | grep -v '^$' || true)
    
    if [ -z "$STAT_DATA_LINES_FULL" ]; then
      STAT_DATA_LINES_FULL=$(git log --author="$AUTHOR_NAME" --no-merges --numstat --pretty="format:" ${MERGE_BASE}..${PARENT_1_SHA} ${MERGE_BASE}..${PARENT_2_SHA} | grep -v '^-' | grep -v '^$' || true)
    fi
    
    if [ -n "$STAT_DATA_LINES_FULL" ]; then
      STAT_DATA_LINES="$STAT_DATA_LINES_FULL"
    fi
  fi
  
  if [ -n "$STAT_DATA_LINES" ]; then
      while IFS= read -r stat_line; do
          if [ -n "$stat_line" ]; then  # 确保stat_line不为空
              added=$(echo "$stat_line" | awk -F'	' '{print $1}')
              deleted=$(echo "$stat_line" | awk -F'	' '{print $2}')

              if [[ "$added" =~ ^[0-9]+$ ]]; then
                  TOTAL_LINES_ADDED_BY_AUTHOR=$((TOTAL_LINES_ADDED_BY_AUTHOR + added))
              fi
              if [[ "$deleted" =~ ^[0-9]+$ ]]; then
                  TOTAL_LINES_DELETED_BY_AUTHOR=$((TOTAL_LINES_DELETED_BY_AUTHOR + deleted))
              fi
          fi
      done <<< "$STAT_DATA_LINES"
  fi

  # 获取 $COMMIT_AUTHOR 在本次合并中引入的所有提交（包括合并提交）所影响的唯一文件列表
  FILES_TOUCHED_BY_AUTHOR_LOG=""
  
  # 使用与统计行数相同的逻辑获取文件列表
  if [ -n "$ALL_COMMITS_BY_AUTHOR" ]; then
    # 对每个找到的提交，获取其修改的文件列表
    while IFS= read -r commit_hash; do
      if [ -n "$commit_hash" ]; then
        commit_files=$(git show --name-only --pretty="format:" $commit_hash | grep -v '^$' || true)
        if [ -n "$commit_files" ]; then
          if [ -n "$FILES_TOUCHED_BY_AUTHOR_LOG" ]; then
            FILES_TOUCHED_BY_AUTHOR_LOG="$FILES_TOUCHED_BY_AUTHOR_LOG"$'\n'"$commit_files"
          else
            FILES_TOUCHED_BY_AUTHOR_LOG="$commit_files"
          fi
        fi
      fi
    done <<< "$ALL_COMMITS_BY_AUTHOR"
    
    # 去重处理
    if [ -n "$FILES_TOUCHED_BY_AUTHOR_LOG" ]; then
      FILES_TOUCHED_BY_AUTHOR_LOG=$(echo "$FILES_TOUCHED_BY_AUTHOR_LOG" | sort -u)
    fi
  fi
  
  # 如果上面的方法还是没有结果，尝试直接的范围查询
  if [ -z "$FILES_TOUCHED_BY_AUTHOR_LOG" ]; then
    # 尝试完整的作者信息匹配
    FILES_TOUCHED_BY_AUTHOR_LOG=$(git log --author="$COMMIT_AUTHOR" --name-only --pretty="format:" $PARENT_1_SHA..$CI_COMMIT_SHA | grep -v '^$' | sort -u || true)
    
    # 如果没有结果，尝试只用作者名字匹配
    if [ -z "$FILES_TOUCHED_BY_AUTHOR_LOG" ]; then
      FILES_TOUCHED_BY_AUTHOR_LOG=$(git log --author="$AUTHOR_NAME" --name-only --pretty="format:" $PARENT_1_SHA..$CI_COMMIT_SHA | grep -v '^$' | sort -u || true)
    fi
  fi
  
  # 最后的尝试 - 直接从整个合并中查找文件
  if [ -z "$FILES_TOUCHED_BY_AUTHOR_LOG" ] && [ -n "$MERGE_BASE" ]; then
    FILES_TOUCHED_FULL=$(git log --author="$COMMIT_AUTHOR" --name-only --pretty="format:" ${MERGE_BASE}..${PARENT_1_SHA} ${MERGE_BASE}..${PARENT_2_SHA} | grep -v '^$' | sort -u || true)
    
    if [ -z "$FILES_TOUCHED_FULL" ]; then
      FILES_TOUCHED_FULL=$(git log --author="$AUTHOR_NAME" --name-only --pretty="format:" ${MERGE_BASE}..${PARENT_1_SHA} ${MERGE_BASE}..${PARENT_2_SHA} | grep -v '^$' | sort -u || true)
    fi
    
    if [ -n "$FILES_TOUCHED_FULL" ]; then
      FILES_TOUCHED_BY_AUTHOR_LOG="$FILES_TOUCHED_FULL"
    fi
  fi
  
  if [ -n "$FILES_TOUCHED_BY_AUTHOR_LOG" ]; then
      while IFS= read -r file_path; do
          if [ -n "$file_path" ]; then #确保file_path不为空
             FILES_CHANGED_MAP_BY_AUTHOR["$file_path"]=1
          fi
      done <<< "$FILES_TOUCHED_BY_AUTHOR_LOG"
  fi
  UNIQUE_FILES_CHANGED_BY_AUTHOR=${#FILES_CHANGED_MAP_BY_AUTHOR[@]}

  COMMIT_STATS_LINE="${UNIQUE_FILES_CHANGED_BY_AUTHOR} files changed, ${TOTAL_LINES_ADDED_BY_AUTHOR} insertions(+), ${TOTAL_LINES_DELETED_BY_AUTHOR} deletions(-)"
  # --- 结束新逻辑 ---

  # --- 获取合并相关的提交信息 ---
  # PARENT_2_SHA 来自之前的检查: PARENT_2_SHA=$(git rev-parse --quiet --verify ${CI_COMMIT_SHA}^2 2>/dev/null)
  # MERGE_BASE已经在上面计算过了，这里不需要重复计算

  PARENT_1_LOG_ITEM=""
  if [ -n "$PARENT_1_SHA" ]; then
      PARENT_1_LOG_ITEM=$(git log --pretty="format:- %h %s (%an)" -n 1 $PARENT_1_SHA 2>/dev/null)
  fi

  MERGED_IN_LOGS=""
  # 只有当MERGE_BASE有效且与PARENT_2_SHA不同时，才获取被合入分支的提交
  if [ -n "$MERGE_BASE" ] && [ "$MERGE_BASE" != "$PARENT_2_SHA" ]; then
      MERGED_IN_LOGS=$(git log --pretty="format:- %h %s (%an)" $MERGE_BASE..$PARENT_2_SHA 2>/dev/null)
  fi

  COMBINED_LOGS=""
  if [ -n "$PARENT_1_LOG_ITEM" ]; then
      COMBINED_LOGS="$PARENT_1_LOG_ITEM"
  fi

  if [ -n "$MERGED_IN_LOGS" ]; then
      if [ -n "$COMBINED_LOGS" ]; then
          COMBINED_LOGS="$COMBINED_LOGS\n$MERGED_IN_LOGS"
      else
          COMBINED_LOGS="$MERGED_IN_LOGS"
      fi
  fi

  if [ -n "$COMBINED_LOGS" ]; then
    ADDITIONAL_INFO_FOR_MSG=$(printf "\n\n合入的提交:\n%s" "$COMBINED_LOGS")
  else
    # 如果没有组合日志，确保 ADDITIONAL_INFO_FOR_MSG 是空的
    ADDITIONAL_INFO_FOR_MSG=""
  fi
  # --- 结束获取合并相关的提交信息 ---
else
  # 不是合并提交，使用 git show 来获取当前提交的统计信息
  COMMIT_STATS_LINE=$(git show --shortstat --pretty="format:" $CI_COMMIT_SHA | sed 's/^[[:space:]]*//' | sed '/^$/d')
fi

# 解析统计信息
NUM_FILES_CHANGED=$(echo "$COMMIT_STATS_LINE" | grep -o '[0-9]* file[s]* changed' | grep -o '[0-9]*' || echo "0")
NUM_LINES_ADDED=$(echo "$COMMIT_STATS_LINE" | grep -o '[0-9]* insertion[s]*(+)' | grep -o '[0-9]*' || echo "0")
NUM_LINES_DELETED=$(echo "$COMMIT_STATS_LINE" | grep -o '[0-9]* deletion[s]*(-)' | grep -o '[0-9]*' || echo "0")

# 构建自定义JSON格式
# 注意：将 ADDITIONAL_INFO_FOR_MSG 作为参数传递给 jq
CUSTOM_JSON=$(jq -n -c \
  --arg event "$EVENT_NAME" \
  --arg project "$PROJECT_NAME" \
  --arg sha "$COMMIT_SHA" \
  --arg message "$COMMIT_MESSAGE" \
  --arg author "$COMMIT_AUTHOR" \
  --arg title "${PROJECT_NAME}代码更新" \
  --arg num_files "$NUM_FILES_CHANGED" \
  --arg num_added "$NUM_LINES_ADDED" \
  --arg num_deleted "$NUM_LINES_DELETED" \
  --arg additional_info "$ADDITIONAL_INFO_FOR_MSG" \
  '{
    msg_type: "post",
    content: {
      post: {
        zh_cn: {
          title: $title,
          content:[
            [{
              tag: "text",
              text: ($sha + "/" + $message + "/" + $author + "\n\n变更统计:\n变更文件数: " + $num_files + "\n增加行数: " + $num_added + "\n删除行数: " + $num_deleted + $additional_info)
            }, {
              tag: "at",
              user_id: "all"
             }]
          ]
        }
      }
    }
  }')


echo $CUSTOM_JSON
# 发送自定义JSON到目标Webhook URL
curl -X POST -H "Content-Type: application/json" -d "$CUSTOM_JSON" $GITLAB_YESV_BOT
