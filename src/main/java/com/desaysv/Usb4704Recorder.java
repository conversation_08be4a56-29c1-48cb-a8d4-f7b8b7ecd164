package com.desaysv;

import Automation.BDaq.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import javax.sound.sampled.*;
import java.io.*;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;

@Data
@Slf4j
public class Usb4704Recorder {
    private static final String DEVICE_DESC = "USB-4704,BID#0";

    private static final int START_CHANNEL = 0; // 起始通道号（0表示第1个通道）
    private static final int CHANNEL_COUNT = 1; // 启用通道数量
    private static final int SECTION_LENGTH = 1024; // 每个数据段的采样点数
    private static final int SECTION_COUNT = 0; //  0表示连续流模式（非触发模式）
    private static final int SAMPLE_RATE = 44100; // 采样率
    private static double referenceValue = 0.027209632023516898; // 静息电压做参考
    private float sampleRate; // 采样率 44.1kHz
    private static final int USER_BUFFER_SIZE = CHANNEL_COUNT * SECTION_LENGTH; // 采样率

    private WaveformAiCtrl wfAiCtrl;
    private volatile boolean isRunning;
    // rawData 声明为 CopyOnWriteArrayList（写时复制线程安全集合）
    private final List<Double> rawData = new CopyOnWriteArrayList<>();
    private AtomicReference<Double> currentVolumeDb = new AtomicReference<>(0.0);//存储最新音量

    static {
        // 加载研华驱动DLL
        loadNativeLibrary();
    }

    /**
     * 从jar包中提取并加载native库
     */
    private static void loadNativeLibrary() {
        try {
            String dllName = "Automation.BDaq.dll";
            String resourcePath = "/lib/" + dllName;

            // 首先尝试从外部lib目录加载（开发环境）
            File externalDll = new File("lib/" + dllName);
            if (externalDll.exists()) {
                log.info("从外部目录加载DLL: {}", externalDll.getAbsolutePath());
                loadDllWithDependencies(externalDll.getAbsolutePath());
                return;
            }

            // 从jar包中提取DLL到临时目录
            InputStream dllStream = Usb4704Recorder.class.getResourceAsStream(resourcePath);
            if (dllStream == null) {
                throw new RuntimeException("无法在jar包中找到DLL文件: " + resourcePath);
            }

            // 创建临时文件
            File tempDir = new File(System.getProperty("java.io.tmpdir"), "usb4704_native");
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }

            File tempDll = new File(tempDir, dllName);

            // 如果临时文件已存在且是最新的，直接使用
            if (!tempDll.exists() || shouldUpdateTempFile(dllStream, tempDll)) {
                // 提取DLL到临时文件
                try (FileOutputStream fos = new FileOutputStream(tempDll)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = dllStream.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                    }
                }
                log.info("DLL已提取到临时目录: {}", tempDll.getAbsolutePath());
            } else {
                log.info("使用已存在的临时DLL文件: {}", tempDll.getAbsolutePath());
            }

            dllStream.close();

            // 将临时目录添加到java.library.path
            addToLibraryPath(tempDir.getAbsolutePath());

            // 加载DLL
            loadDllWithDependencies(tempDll.getAbsolutePath());
            log.info("DLL加载成功: {}", tempDll.getAbsolutePath());

            // 设置删除临时文件的钩子
            tempDll.deleteOnExit();

        } catch (Exception e) {
            log.error("加载DLL失败: {}", e.getMessage(), e);
            throw new RuntimeException("无法加载native库", e);
        }
    }

    /**
     * 加载DLL并处理依赖
     */
    private static void loadDllWithDependencies(String dllPath) {
        try {
            // 首先尝试使用System.loadLibrary加载biodaq（如果系统中已安装）
            try {
                System.loadLibrary("biodaq");
                log.info("成功从系统路径加载biodaq库");
            } catch (UnsatisfiedLinkError e) {
                log.warn("无法从系统路径加载biodaq库: {}", e.getMessage());
                // 继续尝试其他方法
            }

            // 加载主DLL
            System.load(dllPath);

        } catch (UnsatisfiedLinkError e) {
            if (e.getMessage().contains("biodaq")) {
                log.error("缺少biodaq依赖库。请确保研华DAQ驱动已正确安装。");
                log.error("解决方案：");
                log.error("1. 安装研华DAQ驱动程序");
                log.error("2. 或将biodaq.dll复制到系统PATH目录");
                log.error("3. 或将biodaq.dll放在与应用程序相同的目录中");
                throw new RuntimeException("缺少biodaq依赖库，请安装研华DAQ驱动", e);
            } else {
                throw e;
            }
        }
    }

    /**
     * 动态添加路径到java.library.path
     */
    private static void addToLibraryPath(String path) {
        try {
            String currentPath = System.getProperty("java.library.path");
            if (currentPath == null || !currentPath.contains(path)) {
                String newPath = currentPath == null ? path : currentPath + File.pathSeparator + path;
                System.setProperty("java.library.path", newPath);

                // 重置ClassLoader的usr_paths字段以使新路径生效
                java.lang.reflect.Field fieldSysPath = ClassLoader.class.getDeclaredField("sys_paths");
                fieldSysPath.setAccessible(true);
                fieldSysPath.set(null, null);

                log.info("已添加路径到java.library.path: {}", path);
            }
        } catch (Exception e) {
            log.warn("无法动态修改java.library.path: {}", e.getMessage());
        }
    }

    /**
     * 检查是否需要更新临时文件
     */
    private static boolean shouldUpdateTempFile(InputStream jarStream, File tempFile) {
        try {
            if (!tempFile.exists()) {
                return true;
            }

            // 简单的大小比较（可以根据需要改进为校验和比较）
            long tempFileSize = tempFile.length();

            // 重新获取jar中的流来计算大小
            try (InputStream sizeStream = Usb4704Recorder.class.getResourceAsStream("/lib/Automation.BDaq.dll")) {
                if (sizeStream == null) {
                    return false;
                }

                long jarFileSize = 0;
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = sizeStream.read(buffer)) != -1) {
                    jarFileSize += bytesRead;
                }

                return tempFileSize != jarFileSize;
            }
        } catch (Exception e) {
            log.warn("检查临时文件时出错，将重新提取: {}", e.getMessage());
            return true;
        }
    }

    private void calibrateReferenceValue() throws Exception {
        log.info("开始校准参考电压...");
        int calibrationSamples = SAMPLE_RATE; // 1秒校准
        List<Double> calibrationData = new ArrayList<>();

        ErrorCode ret = wfAiCtrl.Start();
        if (ret != ErrorCode.Success) {
            throw new Exception("启动设备失败: " + ret);
        }

        double[] buffer = new double[USER_BUFFER_SIZE];
        long startTime = System.nanoTime();
        long timeout = 2000; // 2秒超时

        while (calibrationData.size() < calibrationSamples
                && (System.nanoTime() - startTime) < timeout * 1_000_000L) {
            IntByRef count = new IntByRef();
            ret = wfAiCtrl.GetData(USER_BUFFER_SIZE, buffer, 100, count);
            if (ret == ErrorCode.Success && count.value > 0) {
                for (int i = 0; i < count.value; i++) {
                    calibrationData.add(buffer[i]);
                }
            }
        }
        wfAiCtrl.Stop();

        if (calibrationData.size() < calibrationSamples) {
            throw new Exception("校准失败：无法获取足够样本");
        }

        double sum = 0;
        for (double sample : calibrationData) {
            sum += sample * sample;
        }
        referenceValue = Math.max(0.001, Math.sqrt(sum / calibrationData.size()));
        log.info("校准后的参考电压：{} V", referenceValue);
    }

    public void init( float sampleRate, int channel) throws Exception {
        try {
            DeviceInformation devInfo = new DeviceInformation(DEVICE_DESC);
            log.info("设备信息: {}", devInfo);

            wfAiCtrl = new WaveformAiCtrl();

            // 直接设置设备描述符
            Method setDevice = wfAiCtrl.getClass().getMethod("setSelectedDevice", DeviceInformation.class);
            setDevice.invoke(wfAiCtrl, devInfo);

            // 配置基本采集参数
            wfAiCtrl.getConversion().setChannelStart(channel);
            wfAiCtrl.getConversion().setChannelCount(CHANNEL_COUNT);
            wfAiCtrl.getRecord().setSectionCount(SECTION_COUNT);
            wfAiCtrl.getRecord().setSectionLength(SECTION_LENGTH);

            // 设置采样率
            wfAiCtrl.getConversion().setClockRate(sampleRate);

            // 检查设备是否支持缓冲AI模式
            if (!wfAiCtrl.getFeatures().getBufferedAiSupported()) {
                log.error("设备不支持缓冲AI模式");
                throw new Exception("设备不支持缓冲AI模式");
            }

            if (ErrorCode.Success != wfAiCtrl.Prepare()) {
                log.error("设备初始化失败");
                throw new Exception("设备初始化失败");
            }
            log.info("设备初始化成功");
            calibrateReferenceValue(); // 校准参考电压
        } catch (Exception e) {
            log.error("错误详细信息: {}", e.getMessage());
            throw e;
        }
    }

    public void stopRecord() {
        isRunning = false;
    }

    public void close() {
        if (wfAiCtrl != null) {
            wfAiCtrl.Dispose();
        }
    }

    public void startRecord(int durationSec){
        isRunning = true;
        Thread recordThread = new Thread(() -> {
            try {
                recordData(durationSec);
            } catch (Exception e) {
                log.error("录音过程发生错误: {}", e.getMessage());
            } finally {
                isRunning = false;
            }
        });
        recordThread.setPriority(Thread.MAX_PRIORITY-1); // 设置次高优先级
        recordThread.setDaemon(true); // 设置为守护线程
        recordThread.start();
    }

    /**
     * 录音数据
     */
    public List<Double> recordData(int durationSec) throws Exception {
        ErrorCode ret = wfAiCtrl.Start();
        if (ret != ErrorCode.Success) {
            log.error("设备启动失败: {}", ret.toString());
            throw new Exception("设备启动失败: " + ret.toString());
        }
        log.info("即将录音{}秒", durationSec);
        log.info("开始录音...");

        // 计算需要采集的总样本数
        int expectedSamples = durationSec * SAMPLE_RATE;
        rawData.clear();
        double[] buffer = new double[USER_BUFFER_SIZE];

        while (isRunning && rawData.size() < expectedSamples) {
            IntByRef returnedCount = new IntByRef();
            ret = wfAiCtrl.GetData(USER_BUFFER_SIZE, buffer, 100, returnedCount);  // 设置超时时间为100ms

            if (ret != ErrorCode.Success) {
                log.error("采集错误: {}", ret.toString());
                continue;
            }

            if (returnedCount.value > 0) {
                double blockSum = 0;
                synchronized (rawData) {
                    for (double sample : Arrays.copyOfRange(buffer, 0, returnedCount.value)) {
                        blockSum += sample * sample;
                        rawData.add(sample);
                    }
                }
                double rms = Math.sqrt(blockSum / returnedCount.value);

                // 每1秒打印一次进度
                if (System.nanoTime() % 1_000_000_000 < 100_000_000) {
                    double progress = (double) rawData.size() / expectedSamples * 100;
                    log.info("采集进度：{}%", progress*100);
                }

                // 实时计算当前缓冲区的音量
               //清空 currentVolumeDb
                currentVolumeDb.set(0.0);
                if (rms > 1e-6) { // 防止对零取对数
                    double decibels = 20 * Math.log10(rms / referenceValue);
                    currentVolumeDb.set(decibels);
                } else {
                    currentVolumeDb.set(0.10); // 设置为默认静音值
                }
                log.info("实时音量: {} dB (样本数: {})",
                        String.format("%.1f", currentVolumeDb.get()),
                        rawData.size());

            }
        }

        log.info("\n录音结束，预计应采集{}个样本，实际共采集 {} 个样本", expectedSamples,rawData.size());

        wfAiCtrl.Stop();

        if (rawData.isEmpty()) {
            log.error("没有采集到数据");
            throw new Exception("没有采集到数据");
        }
        // 检查采集到的数据
        double maxValue = 0;
        for (double sample : rawData) {
            maxValue = Math.max(maxValue, Math.abs(sample));
        }
        log.info("数据最大值: {}V", maxValue);
        return rawData;
    }

    // 实时音量计算方法
    public double getCurrentVolume() {
        if (isRunning) {
            // 当处于录音状态时，直接使用已采集数据计算
            double rms = calculateRMS(rawData);
            return (rms > 1e-6) ?
                    20 * Math.log10(rms / referenceValue) :
                    0.10;
        } else {
            // 未录音时执行实时快速采样
            try {
                // 检查设备是否已初始化
                if (wfAiCtrl == null) {
                    log.error("设备未初始化，请先调用init方法");
                    throw new IllegalStateException("设备未初始化，请先调用init方法");
                }

                // 创建临时缓冲区（优化为1024样本）
                final int quickSampleSize = 1024;
                double[] buffer = new double[quickSampleSize];
                IntByRef returnedCount = new IntByRef();

                // 启动设备采集（快速模式）
                ErrorCode ret = wfAiCtrl.Start();
                if (ret != ErrorCode.Success) {
                    log.error("设备启动失败: {}！", ret.toString());
                    return 0.10;
                }

                // 获取实时数据（设置超时50ms）
                ret = wfAiCtrl.GetData(
                        quickSampleSize,
                        buffer,
                        200,
                        returnedCount
                );

                // 立即停止采集
                wfAiCtrl.Stop();

                // 处理采集结果
                if (ret != ErrorCode.Success || returnedCount.value <= 0) {
                    log.error("数据采集失败: {}！", ret.toString());
                    return 0.10;
                }

                // 计算瞬时RMS值
                double sum = 0;
                for (int i = 0; i < returnedCount.value; i++) {
                    sum += buffer[i] * buffer[i];
                }
                double instantRms = Math.sqrt(sum / returnedCount.value);

                // 转换为分贝值
                return (instantRms > 1e-6) ?
                        20 * Math.log10(instantRms / referenceValue) :
                        0.10;

            } catch (Exception e) {
                log.error("实时采样异常: {}", e.getMessage());
                return 0.10; // 返回静音基准值
            }
        }
    }

    /**
     * 应用降噪算法
     */
    public List<Float> applyNoiseReduction(List<Float> data) {
        // 简单阈值降噪（等效Python代码）
        int segmentLength = (int)(SAMPLE_RATE * 0.1);
        for (int i = 0; i < data.size() / segmentLength; i++) {
            int start = i * segmentLength;
            int end = Math.min(start + segmentLength, data.size());

            double max = 0;
            for (int j = start; j < end; j++) {
                max = Math.max(max, Math.abs(data.get(j)));
            }

            if (max < 0.1) {
                for (int j = start; j < end; j++) {
                    data.set(j, (float) 0.0);
                }
            }
        }
        return data;
    }


    /**
     * 保存录音到文件（WAV 格式）
     */
    public void saveAsWav(String fileName, String filePath, List<Float> rawData){
        if (rawData.isEmpty()) {
            log.error("rawData 不能为空！");
            throw new IllegalArgumentException("rawData 不能为空！");
        }
        // 创建数据快照（避免操作原始数据）
        List<Float> snapshotData = new ArrayList<>(rawData);
        if (snapshotData.isEmpty()) { // 改用快照数据
            log.error("数据为空！");
            throw new IllegalArgumentException("数据为空！");
        }
        //降噪
        snapshotData = applyNoiseReduction(snapshotData);
        log.info("降噪后数据：{}个", snapshotData.size());
        // 归一化并转为16位PCM
        double max = snapshotData.stream()
                .map(Math::abs)
                .max(Double::compare)
                .orElse((float)1.0);

        if (max == 0) {
            log.error("所有样本值为 0，无法归一化！");
            max = 1.0; // 设置默认值以避免除零错误
        }

        byte[] pcmBytes = new byte[snapshotData.size() * 2];
        ByteBuffer buffer = ByteBuffer.wrap(pcmBytes);
        buffer.order(ByteOrder.LITTLE_ENDIAN);

        for (double sample : snapshotData) {
            short pcmValue = (short) Math.max(-32768, Math.min(32767, (sample / max) * 32767));
            buffer.putShort(pcmValue);
        }

        // 写入WAV文件
        // 创建目标文件
        File outputFile = new File(filePath, fileName);
        AudioFormat format = new AudioFormat(SAMPLE_RATE, 16, 1, true, false);
        try (ByteArrayInputStream bais = new ByteArrayInputStream(pcmBytes);
             AudioInputStream ais = new AudioInputStream(bais, format, pcmBytes.length / 2)) {
            AudioSystem.write(ais, AudioFileFormat.Type.WAVE, outputFile);
        } catch (IOException e) {
            log.error("保存音频文件失败！{}", e.getMessage());
        }

        // 日志输出
        log.info("文件已保存: {}", outputFile.getAbsolutePath());
        log.info("文件大小: {} 字节", outputFile.length());
        log.info("理论时长: {} 秒", (double) snapshotData.size() / SAMPLE_RATE);
    }

    /**
     * 在给定的时间内检测声音是否在指定的RMS阈值范围内
     *
     * @param durationSeconds 持续时间（秒）
     * @param minDecibels     最小分贝
     * @param maxDecibels     最大分贝
     * @return 是否检测到声音在范围内
     */
    public boolean detectSound(int durationSeconds, double minDecibels, double maxDecibels) {
        try {
            List<Double> rawData = recordData(durationSeconds);
            double rms = calculateRMS(rawData);
            double decibels = 20 * Math.log10(rms/referenceValue);

            return decibels >= minDecibels && decibels <= maxDecibels;
        } catch (Exception e) {
            log.error("检测异常: {}", e.getMessage());
           return false;
        }
    }

    //计算RMS
    public double calculateRMS(List<Double> rawData) {
        double sum = 0;
        for (double sample : rawData) {
            sum += sample * sample;
        }
        return Math.sqrt(sum / rawData.size());
    }

    public static void main(String[] args) {
        Usb4704Recorder recorder = new Usb4704Recorder();
        try {
            recorder.init(SAMPLE_RATE, START_CHANNEL);
            System.out.println("设备初始化成功");

            int recordSeconds = 10;
            System.out.println("准备录制 " + recordSeconds + " 秒的音频...");

            recorder.startRecord(recordSeconds);

            // 等待录音完成
            for (int i = 0; i < recordSeconds; i++) {
                Thread.sleep(20000);
                System.out.println("已录制 " + (i + 1) + " 秒");
            }

            recorder.stopRecord();
            System.out.println("录音已停止");
//            recorder.saveAsWav("recorded_new2.wav", "D:\\daima\\ad4704\\record_file");

            // 确保数据处理完成
            Thread.sleep(1000);

        } catch (Exception e) {
            System.out.println("发生错误: " + e.getMessage());
        } finally {
            recorder.close();
            System.out.println("程序结束");
            System.exit(0); // 确保程序完全退出
        }
    }

}